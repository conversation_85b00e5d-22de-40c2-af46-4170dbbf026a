# YOLO Service

基于FastAPI的YOLO图像检测服务，支持多种YOLO模型和BERT文本处理。

## 功能特性

- **多YOLO模型支持**: OurYOLO、DocLayout、YOLO表格检测
- **BERT文本处理**: 支持中文文本分类和格式化
- **完善的日志系统**: 支持控制台和文件双重输出，自动日志轮转
- **配置管理**: 基于Pydantic的配置管理，支持环境变量

## 日志系统

### 日志特性

- **双重输出**: 同时输出到控制台和文件
- **日志轮转**: 自动按文件大小进行日志轮转，避免单个文件过大
- **分级日志**: 普通日志和错误日志分别存储
- **详细格式**: 包含时间戳、模块名、文件名、行号等详细信息
- **可配置**: 通过配置文件或环境变量灵活配置

### 日志文件结构

```
logs/
├── yolo_service_20231201.log          # 主日志文件（所有级别）
├── yolo_service_20231201.log.1        # 轮转备份文件
├── yolo_service_20231201.log.2
├── yolo_service_error_20231201.log    # 错误日志文件（仅ERROR和CRITICAL）
└── yolo_service_error_20231201.log.1
```

### 日志配置

在 `.env` 文件中配置日志参数：

```bash
# 日志级别: DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_LEVEL=INFO

# 日志目录
LOG_DIR=logs

# 单个日志文件最大大小（字节）
LOG_MAX_BYTES=10485760  # 10MB

# 保留的备份文件数量
LOG_BACKUP_COUNT=5

# 是否输出到控制台
LOG_TO_CONSOLE=true

# 是否输出到文件
LOG_TO_FILE=true
```

### 日志格式

**控制台输出格式**:
```
2023-12-01 10:30:45,123 - INFO - === YOLO图像检测服务启动 ===
```

**文件输出格式**:
```
2023-12-01 10:30:45,123 - app - INFO - app.py:30 - === YOLO图像检测服务启动 ===
```

## API接口

### 1. YOLO图像检测

**POST** `/yolo-ocr/inference`

```json
{
  "image": "base64_encoded_image",
  "model_type": "ouryolo",
  "conf": 0.5
}
```

支持的模型类型：
- `ouryolo`: OurYOLO模型
- `doclayout`: DocLayout模型  
- `yolotabledet`: YOLO表格检测模型

### 2. BERT文本处理

**POST** `/bert/inference`

```json
{
  "text": "需要处理的文本内容"
}
```

## 快速开始

1. **安装依赖**:
   ```bash
   pip install -r requirements.txt
   ```

2. **配置环境**:
   ```bash
   cp .env.example .env
   # 编辑 .env 文件，配置模型路径等参数
   ```

3. **启动服务**:
   ```bash
   python app.py
   ```

4. **查看日志**:
   ```bash
   # 实时查看日志
   tail -f logs/yolo_service_$(date +%Y%m%d).log
   
   # 查看错误日志
   tail -f logs/yolo_service_error_$(date +%Y%m%d).log
   ```

## 配置说明

所有配置项都可以通过环境变量或 `.env` 文件进行设置。详细配置请参考 `.env.example` 文件。

### 主要配置项

- **模型路径**: 配置各种YOLO模型和BERT模型的路径
- **设备配置**: 支持CPU和GPU，可指定具体GPU设备
- **BERT配置**: dropout率等模型参数
- **日志配置**: 日志级别、目录、轮转等设置

## 架构说明

- **app.py**: FastAPI应用主文件，定义API接口
- **services.py**: 业务逻辑层，包含模型服务类
- **models.py**: Pydantic数据模型定义
- **config.py**: 配置管理
- **utils.py**: 工具函数
- **logging_config.py**: 日志配置模块

## 日志示例

服务运行时的典型日志输出：

```
2023-12-01 10:30:45,123 - INFO - === YOLO图像检测服务启动 ===
2023-12-01 10:30:45,124 - INFO - 日志级别: INFO
2023-12-01 10:30:45,125 - INFO - 设备配置: auto
2023-12-01 10:30:45,126 - INFO - 开始加载模型，使用设备: cpu
2023-12-01 10:30:45,127 - INFO - 正在加载YOLO模型...
2023-12-01 10:30:50,234 - INFO - OurYOLO模型加载完成: /path/to/best.pt
2023-12-01 10:30:55,345 - INFO - DocLayout模型加载完成: /path/to/doclayout.pt
2023-12-01 10:31:00,456 - INFO - YOLO表格检测模型加载完成: /path/to/table_yolo.pt
2023-12-01 10:31:05,567 - INFO - 正在加载BERT模型...
2023-12-01 10:31:10,678 - INFO - BERT tokenizer加载完成: /path/to/bert-base-chinese
2023-12-01 10:31:15,789 - INFO - BERT模型加载完成: /path/to/bert_best.pt
2023-12-01 10:31:15,790 - INFO - 所有模型加载成功
2023-12-01 10:31:15,791 - INFO - === 所有模型加载完成，服务就绪 ===
```
