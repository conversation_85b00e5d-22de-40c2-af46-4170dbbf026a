import logging
import logging.handlers
import sys
from datetime import datetime
from pathlib import Path

def setup_logging(
    log_level: str = "INFO",
    log_dir: str = "logs",
    service_name: str = "yolo_service",
    max_bytes: int = 10 * 1024 * 1024,  # 10MB
    backup_count: int = 5,
    console_output: bool = True,
    file_output: bool = True
):
    """
    设置日志配置
    
    Args:
        log_level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_dir: 日志目录
        service_name: 服务名称，用于日志文件命名
        max_bytes: 单个日志文件最大大小（字节）
        backup_count: 保留的日志文件数量
        console_output: 是否输出到控制台
        file_output: 是否输出到文件
    """
    
    # 创建日志目录
    log_path = Path(log_dir)
    log_path.mkdir(exist_ok=True)
    
    # 设置日志级别
    numeric_level = getattr(logging, log_level.upper(), logging.INFO)
    
    # 创建根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(numeric_level)
    
    # 清除现有的处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 日志格式
    detailed_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s'
    )
    simple_formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s'
    )
    
    handlers = []
    
    # 控制台处理器
    if console_output:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(numeric_level)
        console_handler.setFormatter(simple_formatter)
        handlers.append(console_handler)
    
    # 文件处理器
    if file_output:
        # 主日志文件（所有级别）
        log_filename = log_path / f"{service_name}_{datetime.now().strftime('%Y%m%d')}.log"
        file_handler = logging.handlers.RotatingFileHandler(
            log_filename,
            maxBytes=max_bytes,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(numeric_level)
        file_handler.setFormatter(detailed_formatter)
        handlers.append(file_handler)
        
        # 错误日志文件（仅ERROR和CRITICAL）
        error_log_filename = log_path / f"{service_name}_error_{datetime.now().strftime('%Y%m%d')}.log"
        error_handler = logging.handlers.RotatingFileHandler(
            error_log_filename,
            maxBytes=max_bytes,
            backupCount=backup_count,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(detailed_formatter)
        handlers.append(error_handler)
    
    # 添加处理器到根日志器
    for handler in handlers:
        root_logger.addHandler(handler)
    
    # 设置第三方库的日志级别
    logging.getLogger('uvicorn').setLevel(logging.WARNING)
    logging.getLogger('fastapi').setLevel(logging.WARNING)
    logging.getLogger('ultralytics').setLevel(logging.WARNING)
    logging.getLogger('transformers').setLevel(logging.WARNING)
    
    return root_logger

def get_logger(name: str = None) -> logging.Logger:
    """
    获取日志器实例
    
    Args:
        name: 日志器名称，默认使用调用模块的名称
        
    Returns:
        logging.Logger: 日志器实例
    """
    return logging.getLogger(name)
