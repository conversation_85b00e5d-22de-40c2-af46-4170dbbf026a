#!/bin/bash

# 定义服务地址
SERVICE_URL="http://localhost:8000"

# 检查是否提供了图片文件路径
if [ -z "$1" ]; then
  echo "用法: $0 <图片文件路径> [模型类型]"
  echo "模型类型可选: ouryolo, doclayout, yolotabledet (默认: ouryolo)"
  exit 1
fi

IMAGE_PATH="$1"
MODEL_TYPE="${2:-ouryolo}"

# 检查图片文件是否存在
if [ ! -f "$IMAGE_PATH" ]; then
  echo "错误: 图片文件 '$IMAGE_PATH' 不存在。"
  exit 1
fi

# 验证模型类型
case "$MODEL_TYPE" in
  ouryolo|doclayout|yolotabledet)
    ;;
  *)
    echo "错误: 无效的模型类型 '$MODEL_TYPE'。支持的类型: ouryolo, doclayout, yolotabledet"
    exit 1
    ;;
esac

# 将图片文件转换为base64编码
BASE64_IMAGE=$(base64 "$IMAGE_PATH" | tr -d '\n')

echo "=== 测试 YOLO 图像检测接口 ==="
echo "图片路径: $IMAGE_PATH"
echo "模型类型: $MODEL_TYPE"
echo "服务地址: $SERVICE_URL"
echo ""

# 测试YOLO图像检测接口
echo "=== 调用 /yolo-ocr/inference 接口 ==="
curl -X POST \
  -H "Content-Type: application/json" \
  -d "{
    \"image\": \"$BASE64_IMAGE\",
    \"model_type\": \"$MODEL_TYPE\",
    \"conf\": 0.5
  }" \
  $SERVICE_URL/yolo-ocr/inference

echo -e "\n\n=== 测试 BERT 文本处理接口 ==="
curl -X POST \
  -H "Content-Type: application/json" \
  -d "{
    \"text\": \"这是一个测试文本，用于验证BERT模型的文本处理功能。\"
  }" \
  $SERVICE_URL/bert/inference

echo -e "\n\n=== 测试完成 ==="