FROM swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/nvidia/cuda:12.1.1-cudnn8-devel-ubuntu22.04

RUN set -eux; \
    dpkgArch="$(dpkg --print-architecture)"; \
    case "${dpkgArch##*-}" in \
    amd64) sed -i "s@http://.*archive.ubuntu.com@http://mirrors.ustc.edu.cn@g" /etc/apt/sources.list; \
    sed -i "s@http://.*security.ubuntu.com@http://mirrors.ustc.edu.cn@g" /etc/apt/sources.list;; \
    arm64) sed -i "s@http://ports.ubuntu.com/ubuntu-ports@http://mirrors.ustc.edu.cn/ubuntu-ports@g" /etc/apt/sources.list;; \
    esac;

ENV DEBIAN_FRONTEND=noninteractive
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    python3.10 \
    python3.10-dev \
    python3-pip \
    libgl1-mesa-glx \
    less \
    wget \
    curl \
    vim \
    net-tools \
    iputils-ping \
    dnsutils \
    ttf-wqy-zenhei \
    fonts-wqy-zenhei \
    poppler-utils \
    libcairo2-dev \
    libgirepository1.0-dev \
    && apt-get clean && \
    rm -rf /var/lib/apt/lists/*

RUN curl -o /usr/local/bin/uv "$(curl -u 'user:MzvkQb8ns_DHcG29KaR2' -s -L -w '%{url_effective}' -o /dev/null 'https://gitlab.dipeak.com/api/v4/projects/33/packages/generic/uv/0.6.14/uv')"
RUN chmod +x /usr/local/bin/uv

RUN uv pip install -i https://www.paddlepaddle.org.cn/packages/stable/cu118/ paddlepaddle-gpu==3.0.0rc1 --system

ADD nl2metric/nl2document/builder/paddle_service/requirements.txt /tmp/requirements.txt 

RUN uv pip install -i https://mirrors.bfsu.edu.cn/pypi/web/simple -r /tmp/requirements.txt --system


RUN python3 -c "from paddlex.utils.fonts import get_font_file_path; \
get_font_file_path('PingFang-SC-Regular.ttf'); \
get_font_file_path('simfang.ttf')"

ADD nl2metric/nl2document/builder/paddle_service /builder/paddle_service

ADD copy-doc-builder-resource.py /builder/paddle_service/

WORKDIR /builder/paddle_service

RUN python3 copy-doc-builder-resource.py

CMD ["python3", "app.py"]
